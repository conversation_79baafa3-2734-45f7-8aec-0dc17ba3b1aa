@echo off
echo Setting up Maven environment...

REM Set JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-24
echo JAVA_HOME set to: %JAVA_HOME%

REM Add Maven to PATH
set PATH=%PATH%;C:\Users\<USER>\Downloads\apache-maven-3.9.11-bin\apache-maven-3.9.11\bin
echo Maven added to PATH

REM Test Maven
echo Testing Maven...
mvn -version

echo.
echo Maven setup complete! You can now use 'mvn' commands.
echo To make this permanent, restart your terminal or run this batch file again.
pause
