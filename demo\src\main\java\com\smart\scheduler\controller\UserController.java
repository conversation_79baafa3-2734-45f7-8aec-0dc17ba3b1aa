package com.smart.scheduler.controller;

import com.smart.scheduler.entity.User;
import com.smart.scheduler.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserRepository userRepository;

    // POST: Register a new user
    @PostMapping
    public User createUser(@RequestBody User user) {
        return userRepository.save(user);
    }

    // GET: List all users
    @GetMapping
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }
}
